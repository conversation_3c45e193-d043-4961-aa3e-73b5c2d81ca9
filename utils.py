import requests
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional, Any
import re # For making URL absolute

# Helper to make URLs absolute
from urllib.parse import urljoin

def parse_cap_xml_content(xml_content: str, cap_url: str) -> Optional[Dict[str, Any]]:
    """
    Parses the string content of a CAP XML file.
    CAP XML has a namespace, which we need to handle.
    """
    try:
        root = ET.fromstring(xml_content)
        # CAP 1.2 namespace
        ns = {'cap': 'urn:oasis:names:tc:emergency:cap:1.2'}

        # Helper to find text, handling namespace
        def find_text(element, path):
            el = element.find(path, ns)
            return el.text if el is not None else None

        # Extracting common CAP elements
        # Multiple <info> blocks can exist, often for different languages or updates.
        # We'll take the first <info> block for simplicity, or try to find an English one.
        info_element = root.find('cap:info', ns)
        if info_element is None:
            # Fallback if no <info> with explicit namespace prefix (less common)
            info_element = root.find('{urn:oasis:names:tc:emergency:cap:1.2}info')
            if info_element is None:
                print(f"Warning: No <info> block found in CAP file: {cap_url}")
                return None # Or handle more gracefully

        # Try to find an English language info block if multiple exist
        english_info = None
        all_info_blocks = root.findall('cap:info', ns)
        if not all_info_blocks: # try without explicit ns prefix
             all_info_blocks = root.findall('{urn:oasis:names:tc:emergency:cap:1.2}info')

        for info_block_candidate in all_info_blocks:
            lang = find_text(info_block_candidate, 'cap:language')
            if lang and lang.lower() == 'en-us': # Common for English
                 english_info = info_block_candidate
                 break
            if lang and lang.lower().startswith('en'): # Any English variant
                 english_info = info_block_candidate
                 break
        if english_info is not None:
            info_element = english_info
        elif all_info_blocks: # Default to the first one if no specific English one found
            info_element = all_info_blocks[0]
        else: # If still no info_element (e.g. from fallback above)
            print(f"Warning: No <info> block could be selected in CAP file: {cap_url}")
            return None


        event = find_text(info_element, 'cap:event')
        description = find_text(info_element, 'cap:description')
        headline = find_text(info_element, 'cap:headline')
        effective_str = find_text(info_element, 'cap:effective')
        expires_str = find_text(info_element, 'cap:expires')
        sent_str = find_text(root, 'cap:sent') # <sent> is child of <alert>

        # Convert times to datetime objects (assuming UTC 'Z' or timezone offset)
        def parse_cap_datetime(dt_str):
            if not dt_str:
                return None
            try:
                # Handle 'Z' for UTC and timezone offsets like -07:00 or +01:00
                if dt_str.endswith('Z'):
                    dt_str = dt_str[:-1] + '+00:00'
                return datetime.fromisoformat(dt_str)
            except ValueError:
                print(f"Warning: Could not parse datetime string: {dt_str}")
                return None

        return {
            "identifier": find_text(root, 'cap:identifier'),
            "sender": find_text(root, 'cap:sender'),
            "sent": parse_cap_datetime(sent_str),
            "status": find_text(root, 'cap:status'),
            "msgType": find_text(root, 'cap:msgType'),
            "scope": find_text(root, 'cap:scope'),
            "event": event,
            "headline": headline,
            "description": description,
            "effective": parse_cap_datetime(effective_str),
            "expires": parse_cap_datetime(expires_str),
            "cap_url": cap_url
        }
    except ET.ParseError as e:
        print(f"Error parsing XML from {cap_url}: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while parsing CAP XML from {cap_url}: {e}")
        return None


def get_ghana_meteo_alerts(
    disaster_keywords: List[str],
    main_alerts_url: str = "https://www.meteo.gov.gh/alerts/"
) -> List[Dict[str, Any]]:
    """
    Fetches CAP alerts from the Ghana Meteorological Agency website.

    It scrapes the main alerts page to find links to individual CAP XML files,
    then parses those files and filters them by disaster keywords.
    It prioritizes active alerts. If no active alerts matching keywords are found,
    it returns the most recently expired matching alert(s).

    Args:
        disaster_keywords: List of keywords to search for (e.g., ["Thunderstorm", "Squall line"]).
                           Case-insensitive.
        main_alerts_url: The URL of the page listing alerts.

    Returns:
        A list of dictionaries, where each dictionary represents a matching CAP alert.
        Returns an empty list if no matching alerts are found or in case of errors.
    """
    alerts = []
    try:
        response = requests.get(main_alerts_url, timeout=20)
        response.raise_for_status()
    except requests.RequestException as e:
        print(f"Error fetching main alerts page {main_alerts_url}: {e}")
        return alerts

    soup = BeautifulSoup(response.content, 'html.parser')
    cap_xml_urls = []

    # Find links to CAP files.
    # Based on observation, alerts are in a table. Each row (`<tr>`) has alert info.
    # The link with text "CAP" in its cell (`<td>`) is the target.
    # This parsing logic might need adjustment if the website structure changes.
    
    # First, try finding table rows in a table with id 'datatable' or class 'table'
    table = soup.find('table', id='datatable') 
    if not table:
        table = soup.find('table', class_='table') # A common class for tables
    
    alert_rows = []
    if table:
        alert_rows = table.find_all('tr')
    else:
        # Fallback: search for all 'a' tags with text 'CAP' if no obvious table structure
        # This is less precise.
        all_cap_links = soup.find_all('a', string=re.compile(r"\s*CAP\s*"))
        for link in all_cap_links:
            href = link.get('href')
            if href and href.endswith('.xml'): # Ensure it's an XML link
                 # Make URL absolute
                cap_xml_urls.append(urljoin(main_alerts_url, href))
        if not cap_xml_urls:
             print("Warning: Could not find a table with alerts, and no direct 'CAP' links ending in .xml found. Trying a broader search for alert links.")
             # Broader search (example from previous thought process if each alert is a div)
             # This part needs to be adapted based on actual HTML structure from a browser's inspector tool
             # if the table search fails. For now, we'll assume the table or direct CAP links work.


    if table and alert_rows:
        print(f"Found {len(alert_rows)-1} potential alert rows in table.") # -1 for header
        for row in alert_rows:
            # Find all `a` tags within this row
            links_in_row = row.find_all('a')
            for link_tag in links_in_row:
                # Check if the link's text is "CAP" (stripping whitespace)
                if link_tag.get_text(strip=True).upper() == "CAP":
                    href = link_tag.get('href')
                    if href:
                        # Make URL absolute if it's relative
                        cap_xml_url = urljoin(main_alerts_url, href)
                        if cap_xml_url.endswith('.xml'): # Good heuristic
                            cap_xml_urls.append(cap_xml_url)
                        break # Found CAP link for this row

    if not cap_xml_urls:
        print("No CAP XML URLs found on the page. The HTML structure might have changed or no alerts are listed in the expected format.")
        return alerts
    
    print(f"Found {len(cap_xml_urls)} potential CAP XML URLs: {cap_xml_urls[:5]}...") # Print first 5

    parsed_alerts = []
    for xml_url in list(set(cap_xml_urls)): # Use set to avoid duplicate URLs if any
        print(f"  Fetching CAP XML: {xml_url}")
        try:
            xml_response = requests.get(xml_url, timeout=15)
            xml_response.raise_for_status()
            # It's good to check content-type, but some servers might not set it perfectly for XML
            # if not xml_response.headers.get('Content-Type','').lower().startswith(('application/xml', 'text/xml')):
            #    print(f"    Warning: Content-Type for {xml_url} is not XML ({xml_response.headers.get('Content-Type')}). Skipping.")
            #    continue

            cap_data = parse_cap_xml_content(xml_response.content.decode('utf-8', errors='replace'), xml_url) # Ensure decoding
            if cap_data:
                # Check for keywords
                text_to_search = f"{cap_data.get('event','')} {cap_data.get('headline','')} {cap_data.get('description','')}".lower()
                matched_kw = [kw for kw in disaster_keywords if kw.lower() in text_to_search]
                
                if matched_kw:
                    cap_data['matched_keywords'] = matched_kw
                    parsed_alerts.append(cap_data)

        except requests.RequestException as e:
            print(f"    Error fetching CAP XML {xml_url}: {e}")
        except Exception as e:
            print(f"    Error processing CAP XML {xml_url}: {e}")


    if not parsed_alerts:
        print("No alerts found matching the keywords.")
        return []

    active_alerts = []
    expired_alerts = []
    now = datetime.now(timezone.utc)

    for alert in parsed_alerts:
        effective = alert.get('effective')
        expires = alert.get('expires')
        
        is_active = False
        if effective and expires:
            if effective <= now < expires:
                is_active = True
        elif effective and not expires: # No expiration means active indefinitely after effective time
             if effective <= now:
                is_active = True
        # If only expires is present, it's a bit ambiguous, usually means active until then
        elif not effective and expires:
            if now < expires:
                 is_active = True
        # If neither, can't determine active status from times alone, might check 'status' field
        # or assume active if recently sent (e.g., within last 24-48 hours) - but this is heuristic
        # For now, strict check on effective/expires.

        # Also consider CAP status: "Actual" is what we usually want.
        if alert.get("status") != "Actual":
            is_active = False # Only "Actual" messages are considered for active/expired status here

        if is_active:
            active_alerts.append(alert)
        else:
            expired_alerts.append(alert)

    if active_alerts:
        print(f"Returning {len(active_alerts)} active alert(s) matching keywords.")
        # Sort by sent time, most recent first
        active_alerts.sort(key=lambda x: x.get('sent') or datetime.min.replace(tzinfo=timezone.utc), reverse=True)
        return active_alerts
    elif expired_alerts:
        print(f"No active alerts. Returning most recent {min(1, len(expired_alerts))} expired alert(s) matching keywords.")
        # Sort by expires time (most recently expired first) or sent time if expires is not available
        expired_alerts.sort(key=lambda x: x.get('expires') or x.get('sent') or datetime.min.replace(tzinfo=timezone.utc), reverse=True)
        return expired_alerts[:1] # Return the single most recent expired alert
    else:
        print("No active or expired alerts found matching criteria after parsing.")
        return []

if __name__ == '__main__':
    print("--- Fetching Ghana Meteo Alerts for Thunderstorms/Squall Lines ---")
    
    # Define keywords for the disaster type
    keywords = ["Thunderstorm", "Thunderstorms", "Squall line", "Squall lines", "Squall"]

    # Ghana Meteorological Agency alerts page
    # url = "https://www.meteo.gov.gh/alerts/" # Production URL
    # For testing, if you have a local HTML copy or a test server:
    # url = "http://localhost:8000/ghana_meteo_alerts.html" 
    
    # Since I can't run a local webserver or guarantee the live site's structure
    # won't change, this example call might not produce output if the live site
    # has no matching alerts or if its HTML structure differs from assumptions.
    
    # For robust testing, one would typically:
    # 1. Save a copy of the live HTML page.
    # 2. Save copies of a few linked CAP XML files.
    # 3. Adapt the parser (especially the BeautifulSoup part) to the saved HTML.
    # 4. Test the XML parsing with the saved XML files.
    
    # Example with a direct CAP XML file for testing the XML parsing part:
    # (This URL was provided by the user earlier as an example of a CAP file)
    test_cap_url = "https://www.meteo.gov.gh/api/cap/03cb5a35-8a1e-42a0-a42b-baf331cb000c.xml"
    print(f"\n--- Testing XML parsing with a single known CAP URL: {test_cap_url} ---")
    try:
        single_xml_response = requests.get(test_cap_url, timeout=10)
        single_xml_response.raise_for_status()
        # Manually decode, as server might not specify utf-8 sometimes, though it should
        xml_content_single = single_xml_response.content.decode('utf-8', errors='replace') 
        parsed_single_cap = parse_cap_xml_content(xml_content_single, test_cap_url)
        if parsed_single_cap:
            print("Successfully parsed single CAP XML:")
            for key, value in parsed_single_cap.items():
                print(f"  {key}: {value}")
            
            # Test keyword matching on this single alert
            text_to_search_single = f"{parsed_single_cap.get('event','')} {parsed_single_cap.get('headline','')} {parsed_single_cap.get('description','')}".lower()
            matched_kw_single = [kw for kw in keywords if kw.lower() in text_to_search_single]
            if matched_kw_single:
                print(f"  Matched Keywords in single alert: {matched_kw_single}")
            else:
                print(f"  No matching keywords in single alert for: {keywords}")

        else:
            print("Failed to parse the single CAP XML.")
    except requests.RequestException as e:
        print(f"Could not fetch or parse the single test CAP XML {test_cap_url}: {e}")
    except Exception as e:
        print(f"General error during single CAP XML test: {e}")

    print("\n--- Attempting to fetch and process alerts from the main page ---")
    # This is the main call to the function.
    # It will try to access the live URL.
    ghana_alerts = get_ghana_meteo_alerts(disaster_keywords=keywords)

    if ghana_alerts:
        print(f"\n--- Found {len(ghana_alerts)} matching alert(s) ---")
        for alert_data in ghana_alerts:
            print("\nAlert Details:")
            for key, value in alert_data.items():
                print(f"  {key}: {value}")
    else:
        print("\nNo matching alerts found from the main page process, or an error occurred.")