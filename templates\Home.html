<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Communications Platform - Ghana</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0f;
            color: #fff;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 20px;
            text-align: center;
            z-index: 20;
            pointer-events: auto;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
            background: linear-gradient(90deg, #00d4ff, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            pointer-events: auto;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .data-sources {
            position: absolute;
            bottom: 30px;
            left: 30px;
            z-index: 15;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .source-button {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            cursor: pointer;
            min-width: 250px;
            border: none;
            color: #fff;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .source-button.active {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.5);
        }

        .source-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .cap-alert { background: linear-gradient(135deg, #f39c12, #e74c3c); }
        .meteo { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro { background: linear-gradient(135deg, #27ae60, #16a085); }
        .nadmo { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .fire { background: linear-gradient(135deg, #e67e22, #d35400); }
        .police { background: linear-gradient(135deg, #34495e, #2c3e50); }

        .advisory-panel {
            position: absolute;
            bottom: 30px;
            right: 30px;
            z-index: 15;
            max-width: 500px;
        }

        .advisory-card {
            margin-bottom: 20px;
        }

        .advisory-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .advisory-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .weather-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro-icon { background: linear-gradient(135deg, #27ae60, #16a085); }

        .advisory-content h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: #00d4ff;
        }

        .advisory-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            opacity: 0.8;
        }

        .status-bar {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 15;
            display: flex;
            gap: 30px;
            padding: 15px 30px;
        }

        .status-item {
            text-align: center;
        }

        .status-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00d4ff;
        }

        .status-label {
            font-size: 0.8rem;
            opacity: 0.7;
            text-transform: uppercase;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #0a0a0f;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-top: 2px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Process Animation Overlay */
        .process-animation-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .process-animation-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .process-step {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 15px 0;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .process-step.active {
            opacity: 1;
            transform: translateY(0);
        }

        .process-step.completed {
            opacity: 0.6;
            transform: translateY(-10px);
        }

        .process-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            position: relative;
            animation: float 2s ease-in-out infinite;
        }

        .process-icon.loading {
            animation: float 2s ease-in-out infinite, pulse 1.5s ease-in-out infinite;
        }

        .process-icon::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid transparent;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .process-icon.loading::after {
            border-top: 2px solid rgba(0, 212, 255, 0.8);
            border-right: 2px solid rgba(0, 212, 255, 0.6);
            border-bottom: 2px solid rgba(0, 212, 255, 0.4);
            border-left: 2px solid rgba(0, 212, 255, 0.2);
            animation: spin 1s linear infinite;
        }

        .process-icon.completed::after {
            border: 2px solid #27ae60;
        }

        .process-text {
            color: #fff;
            font-size: 1.1rem;
            font-weight: 300;
            letter-spacing: 1px;
            min-width: 300px;
        }

        .process-text.loading {
            color: #00d4ff;
        }

        .process-text.completed {
            color: #27ae60;
        }

        /* Icon backgrounds for different processes */
        .icon-api { background: linear-gradient(135deg, #3498db, #2980b9); }
        .icon-scrape { background: linear-gradient(135deg, #e67e22, #d35400); }
        .icon-ai { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .icon-process { background: linear-gradient(135deg, #f39c12, #e74c3c); }
        .icon-format { background: linear-gradient(135deg, #27ae60, #16a085); }
        .icon-complete { background: linear-gradient(135deg, #2ecc71, #27ae60); }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .process-animation-title {
            font-size: 1.5rem;
            color: #00d4ff;
            margin-bottom: 30px;
            text-align: center;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <!-- Process Animation Overlay -->
    <div class="process-animation-overlay" id="process-animation">
        <div class="process-animation-title" id="process-title">Fetching Data</div>
        <div id="process-steps-container">
            <!-- Process steps will be dynamically added here -->
        </div>
    </div>

    <div id="canvas-container"></div>

    <div class="overlay">
        <div class="header">
            <h1>Emergency Communications Platform</h1>
        </div>

        <div class="status-bar glass-card">
            <div class="status-item">
                <div class="status-value" id="active-alerts">0</div>
                <div class="status-label">Active Alerts</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="coverage">0</div>
                <div class="status-label">Coverage Area (km²)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="response-time">--</div>
                <div class="status-label">Response Time</div>
            </div>
        </div>

        <div class="data-sources">
            <button class="source-button glass-card" data-source="cap" id="btn-cap">
                <div class="source-icon cap-alert">⚡</div>
                <span>CAP Alert Editor</span>
            </button>
            <button class="source-button glass-card" data-source="meteo" id="btn-meteo">
                <div class="source-icon meteo">🌤️</div>
                <span>Open Meteo API</span>
            </button>
            <button class="source-button glass-card" data-source="agro" id="btn-agro">
                <div class="source-icon agro">🌾</div>
                <span>GHAAP Agro-Climate</span>
            </button>
            <button class="source-button glass-card" data-source="nadmo" id="btn-nadmo">
                <div class="source-icon nadmo">🚨</div>
                <span>NADMO</span>
            </button>
            <button class="source-button glass-card" data-source="fire" id="btn-fire">
                <div class="source-icon fire">🚒</div>
                <span>Fire Service</span>
            </button>
            <button class="source-button glass-card" data-source="police" id="btn-police">
                <div class="source-icon police">👮</div>
                <span>Ghana Police</span>
            </button>
        </div>

        <div class="advisory-panel">
            <div class="advisory-card glass-card" id="weather-advisory">
                <div class="advisory-header">
                    <div class="advisory-icon weather-icon">🌧️</div>
                    <div class="advisory-content">
                        <h3>Weather Advisory</h3>
                        <p id="weather-text">Awaiting data...</p>
                    </div>
                </div>
            </div>
            <div class="advisory-card glass-card" id="agro-advisory">
                <div class="advisory-header">
                    <div class="advisory-icon agro-icon">🌱</div>
                    <div class="advisory-content">
                        <h3>Agro-Climate Advisory</h3>
                        <p id="agro-text">Awaiting data...</p>
                    </div>
                </div>
            </div>
             <div class="advisory-card glass-card" id="cap-alert-display">
                <div class="advisory-header">
                    <div class="advisory-icon cap-alert">⚡</div>
                    <div class="advisory-content">
                        <h3 id="cap-headline">CAP Alert</h3>
                        <p id="cap-description">Awaiting data...</p>
                        <small id="cap-area" style="display: block; margin-top: 5px; opacity: 0.7;"></small>
                        <small id="cap-sender" style="display: block; margin-top: 3px; opacity: 0.7;"></small>
                        <small id="cap-sent-time" style="display: block; margin-top: 3px; opacity: 0.7;"></small>
                    </div>
                </div>
            </div>
            <div class="advisory-card glass-card" id="cap-input-area" style="display: none;">
                <div class="advisory-header">
                    <div class="advisory-icon cap-alert">📝</div>
                    <div class="advisory-content">
                        <h3>Submit CAP Alert XML</h3>
                        <textarea id="cap-xml-input" placeholder="Paste CAP XML here..." style="width: 100%; height: 100px; margin: 10px 0; padding: 8px; border: 1px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white; border-radius: 5px; resize: vertical;"></textarea>
                        <button id="submit-cap-xml" style="background: linear-gradient(135deg, #f39c12, #e74c3c); border: none; color: white; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 0.9rem;">Submit CAP Alert</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        // --- Process Animation System ---
        class ProcessAnimationManager {
            constructor() {
                this.overlay = document.getElementById('process-animation');
                this.title = document.getElementById('process-title');
                this.container = document.getElementById('process-steps-container');
                this.currentSteps = [];
                this.currentStepIndex = 0;
                this.stepDelay = 1000; // 1 second between steps
            }

            // Define process sequences for different data sources
            getProcessSequence(source) {
                const sequences = {
                    'meteo': [
                        { icon: '🌐', iconClass: 'icon-api', text: 'Connecting to Open Meteo API...' },
                        { icon: '📍', iconClass: 'icon-process', text: 'Getting location coordinates...' },
                        { icon: '🌤️', iconClass: 'icon-api', text: 'Fetching weather forecast data...' },
                        { icon: '🤖', iconClass: 'icon-ai', text: 'Processing with AI summarization...' },
                        { icon: '📝', iconClass: 'icon-format', text: 'Formatting weather advisory...' },
                        { icon: '✅', iconClass: 'icon-complete', text: 'Weather data ready!' }
                    ],
                    'agro': [
                        { icon: '🌐', iconClass: 'icon-api', text: 'Connecting to GHAAP.com...' },
                        { icon: '🔍', iconClass: 'icon-scrape', text: 'Attempting AI-powered scraping...' },
                        { icon: '🤖', iconClass: 'icon-ai', text: 'Using ScrapeGraphAI with OpenAI...' },
                        { icon: '🚗', iconClass: 'icon-scrape', text: 'Trying Selenium browser automation...' },
                        { icon: '🎯', iconClass: 'icon-process', text: 'Looking for element ID="wt_smssend"...' },
                        { icon: '📊', iconClass: 'icon-process', text: 'Extracting agricultural data...' },
                        { icon: '🌾', iconClass: 'icon-format', text: 'Processing crop advisory...' },
                        { icon: '✅', iconClass: 'icon-complete', text: 'Agro-climate data ready!' }
                    ],
                    'cap': [
                        { icon: '📄', iconClass: 'icon-process', text: 'Parsing CAP XML data...' },
                        { icon: '🔍', iconClass: 'icon-ai', text: 'Validating alert structure...' },
                        { icon: '📍', iconClass: 'icon-process', text: 'Processing geographic areas...' },
                        { icon: '⚡', iconClass: 'icon-format', text: 'Formatting emergency alert...' },
                        { icon: '✅', iconClass: 'icon-complete', text: 'CAP alert processed!' }
                    ]
                };
                return sequences[source] || [
                    { icon: '🔄', iconClass: 'icon-process', text: 'Processing request...' },
                    { icon: '✅', iconClass: 'icon-complete', text: 'Request completed!' }
                ];
            }

            show(source, title = null) {
                this.currentSteps = this.getProcessSequence(source);
                this.currentStepIndex = 0;

                // Set title
                this.title.textContent = title || `Fetching ${source.toUpperCase()} Data`;

                // Clear container
                this.container.innerHTML = '';

                // Create step elements
                this.currentSteps.forEach((step, index) => {
                    const stepElement = document.createElement('div');
                    stepElement.className = 'process-step';
                    stepElement.innerHTML = `
                        <div class="process-icon ${step.iconClass}">
                            ${step.icon}
                        </div>
                        <div class="process-text">
                            ${step.text}
                        </div>
                    `;
                    this.container.appendChild(stepElement);
                });

                // Show overlay
                this.overlay.classList.add('active');

                // Start animation sequence
                this.animateSteps();
            }

            animateSteps() {
                if (this.currentStepIndex >= this.currentSteps.length) {
                    // All steps completed, hide after a delay
                    setTimeout(() => this.hide(), 1500);
                    return;
                }

                const stepElement = this.container.children[this.currentStepIndex];
                const icon = stepElement.querySelector('.process-icon');
                const text = stepElement.querySelector('.process-text');

                // Show current step
                stepElement.classList.add('active');
                icon.classList.add('loading');
                text.classList.add('loading');

                // Complete previous step
                if (this.currentStepIndex > 0) {
                    const prevStep = this.container.children[this.currentStepIndex - 1];
                    const prevIcon = prevStep.querySelector('.process-icon');
                    const prevText = prevStep.querySelector('.process-text');

                    prevStep.classList.add('completed');
                    prevIcon.classList.remove('loading');
                    prevIcon.classList.add('completed');
                    prevText.classList.remove('loading');
                    prevText.classList.add('completed');
                }

                this.currentStepIndex++;

                // Continue to next step
                setTimeout(() => this.animateSteps(), this.stepDelay);
            }

            hide() {
                this.overlay.classList.remove('active');

                // Reset after animation
                setTimeout(() => {
                    this.container.innerHTML = '';
                    this.currentSteps = [];
                    this.currentStepIndex = 0;
                }, 300);
            }

            // Method to update a specific step's text (for dynamic updates)
            updateStep(stepIndex, newText) {
                if (stepIndex < this.container.children.length) {
                    const stepElement = this.container.children[stepIndex];
                    const textElement = stepElement.querySelector('.process-text');
                    textElement.textContent = newText;
                }
            }

            // Method to add error state to current step
            setError(errorText) {
                if (this.currentStepIndex > 0) {
                    const currentStep = this.container.children[this.currentStepIndex - 1];
                    const icon = currentStep.querySelector('.process-icon');
                    const text = currentStep.querySelector('.process-text');

                    icon.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                    icon.textContent = '❌';
                    icon.classList.remove('loading');
                    text.textContent = errorText;
                    text.style.color = '#e74c3c';
                    text.classList.remove('loading');
                }

                // Hide after showing error
                setTimeout(() => this.hide(), 2000);
            }
        }

        // Initialize the process animation manager
        const processAnimation = new ProcessAnimationManager();
    </script>
    <script>
        // --- Three.js Scene Setup ---
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.getElementById('canvas-container').appendChild(renderer.domElement);

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9);
        directionalLight.position.set(15, 20, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        scene.add(directionalLight);

        // --- Ghana Map from GeoJSON ---
        let ghanaMapGroup;
        // Use Flask static route for serving the GeoJSON file
        const GEOJSON_FILE_PATH = '/static/ghana_regions.json';

        async function loadAndCreateGhanaMap() {
            let geoJsonPath;
            try {
                // Use Flask static route to serve the GeoJSON file
                geoJsonPath = GEOJSON_FILE_PATH;
                console.log(`Attempting to fetch GeoJSON from: ${geoJsonPath}`);
                const response = await fetch(geoJsonPath);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} when fetching ${geoJsonPath}`);
                }
                const geoJsonData = await response.json();
                console.log('GeoJSON data loaded successfully:', geoJsonData.features ? geoJsonData.features.length + ' features found' : 'No features');
                createGhanaMapFromGeoJSON(geoJsonData);
            } catch (error) {
                console.error(`Could not load or parse GeoJSON data from ${geoJsonPath || GEOJSON_FILE_PATH}:`, error);
                createFallbackMap();
            }
        }

        function createFallbackMap() {
            console.warn("Creating fallback map due to GeoJSON loading error.");
            ghanaMapGroup = new THREE.Group();
            const fallbackMaterial = new THREE.MeshPhongMaterial({ color: 0x555555 });
            const fallbackGeom = new THREE.BoxGeometry(8,0.5,8); // Increased from 5x5 to 8x8
            const fallbackMesh = new THREE.Mesh(fallbackGeom, fallbackMaterial);
            fallbackMesh.position.y = -0.5;
            ghanaMapGroup.add(fallbackMesh);
            scene.add(ghanaMapGroup);
        }


        function createGhanaMapFromGeoJSON(geoJsonData) {
            if (!geoJsonData || !geoJsonData.features || geoJsonData.features.length === 0) {
                console.error("GeoJSON data is empty or invalid.");
                createFallbackMap();
                return;
            }

            ghanaMapGroup = new THREE.Group();
            const regionMaterial = new THREE.MeshPhongMaterial({
                color: 0x006A4E,
                shininess: 15,
                side: THREE.DoubleSide,
            });
            const outlineMaterial = new THREE.LineBasicMaterial({ color: 0xFCD116, linewidth: 2 });

            const allPoints = [];
            geoJsonData.features.forEach(feature => {
                if (feature.geometry && feature.geometry.coordinates) {
                    const polygons = feature.geometry.type === 'Polygon'
                        ? [feature.geometry.coordinates]
                        : feature.geometry.coordinates;

                    polygons.forEach(polygonCoordsArray => {
                        const exteriorRing = polygonCoordsArray[0];
                        exteriorRing.forEach(coord => {
                            if (coord && typeof coord[0] === 'number' && typeof coord[1] === 'number') {
                                allPoints.push(new THREE.Vector2(coord[0], coord[1]));
                            } else {
                                console.warn("Invalid coordinate found in GeoJSON:", coord, "Feature:", feature.properties.region);
                            }
                        });
                    });
                }
            });

            if (allPoints.length === 0) {
                console.error("No valid coordinates found in GeoJSON features to create map.");
                createFallbackMap();
                return;
            }

            const boundingBox = new THREE.Box2().setFromPoints(allPoints);
            const center = boundingBox.getCenter(new THREE.Vector2());
            const size = boundingBox.getSize(new THREE.Vector2());

            const maxDim = Math.max(size.x, size.y);
            const sceneScaleFactor = maxDim > 0 ? (15 / maxDim) : 1; // Increased from 10 to 15

            geoJsonData.features.forEach(feature => {
                if (feature.geometry && feature.geometry.coordinates) {
                    const polygons = feature.geometry.type === 'Polygon'
                        ? [feature.geometry.coordinates]
                        : feature.geometry.coordinates;

                    polygons.forEach((polygonCoordsArray, polyIndex) => {
                        const exteriorRingCoords = polygonCoordsArray[0];
                        if (!exteriorRingCoords || exteriorRingCoords.length < 3) {
                            console.warn("Skipping invalid exterior ring for region:", feature.properties.region, "Polygon index:", polyIndex);
                            return;
                        }

                        const regionShape = new THREE.Shape();
                        const firstPoint = exteriorRingCoords[0];
                        if (!firstPoint || typeof firstPoint[0] !== 'number' || typeof firstPoint[1] !== 'number') {
                             console.warn("Skipping shape due to invalid first point for region:", feature.properties.region);
                             return;
                        }
                        regionShape.moveTo(
                            (firstPoint[0] - center.x) * sceneScaleFactor,
                            (firstPoint[1] - center.y) * sceneScaleFactor
                        );

                        for (let i = 1; i < exteriorRingCoords.length; i++) {
                            const point = exteriorRingCoords[i];
                             if (!point || typeof point[0] !== 'number' || typeof point[1] !== 'number') {
                                console.warn("Skipping point due to invalid coordinate for region:", feature.properties.region);
                                continue;
                            }
                            regionShape.lineTo(
                                (point[0] - center.x) * sceneScaleFactor,
                                (point[1] - center.y) * sceneScaleFactor
                            );
                        }

                        for (let h = 1; h < polygonCoordsArray.length; h++) {
                            const holeCoords = polygonCoordsArray[h];
                             if (!holeCoords || holeCoords.length < 3) continue;

                            const holePath = new THREE.Path();
                            const firstHolePoint = holeCoords[0];
                            if (!firstHolePoint || typeof firstHolePoint[0] !== 'number' || typeof firstHolePoint[1] !== 'number') continue;

                            holePath.moveTo(
                                (firstHolePoint[0] - center.x) * sceneScaleFactor,
                                (firstHolePoint[1] - center.y) * sceneScaleFactor
                            );
                            for (let i = 1; i < holeCoords.length; i++) {
                                const holePoint = holeCoords[i];
                                if (!holePoint || typeof holePoint[0] !== 'number' || typeof holePoint[1] !== 'number') continue;
                                holePath.lineTo(
                                    (holePoint[0] - center.x) * sceneScaleFactor,
                                    (holePoint[1] - center.y) * sceneScaleFactor
                                );
                            }
                            regionShape.holes.push(holePath);
                        }


                        const extrudeSettings = {
                            steps: 1,
                            depth: 0.3,
                            bevelEnabled: true,
                            bevelThickness: 0.05,
                            bevelSize: 0.05,
                            bevelOffset: 0,
                            bevelSegments: 1
                        };

                        const geometry = new THREE.ExtrudeGeometry(regionShape, extrudeSettings);

                        const regionMesh = new THREE.Mesh(geometry, regionMaterial.clone());
                        regionMesh.castShadow = true;
                        regionMesh.receiveShadow = true;
                        regionMesh.name = feature.properties.region || `region-${polyIndex}`;
                        ghanaMapGroup.add(regionMesh);

                        const edgesGeom = new THREE.EdgesGeometry(geometry);
                        const regionOutline = new THREE.LineSegments(edgesGeom, outlineMaterial.clone());
                        ghanaMapGroup.add(regionOutline);
                    });
                }
            });

            ghanaMapGroup.rotation.x = -Math.PI / 2;
            ghanaMapGroup.position.y = -0.5;
            scene.add(ghanaMapGroup);
            console.log("Ghana map created from GeoJSON data. Total regions:", ghanaMapGroup.children.length / 2); // Divided by 2 because each region has mesh + outline
        }

        loadAndCreateGhanaMap();

        let currentWeatherEffect = null;

        // Weather effects
        function createRainEffect() {
            const rainGroup = new THREE.Group();
            const rainCount = 200;

            for (let i = 0; i < rainCount; i++) {
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array(6);

                // Random x and z positions
                const x = (Math.random() - 0.5) * 20;
                const z = (Math.random() - 0.5) * 20;
                const y = Math.random() * 15;

                // Line from top to bottom
                positions[0] = x;
                positions[1] = y;
                positions[2] = z;
                positions[3] = x;
                positions[4] = y - 0.5;
                positions[5] = z;

                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

                const material = new THREE.LineBasicMaterial({
                    color: 0x4a90e2,
                    transparent: true,
                    opacity: 0.6,
                    linewidth: 1
                });

                const line = new THREE.Line(geometry, material);
                rainGroup.add(line);
            }

            return rainGroup;
        }

        function createSunEffect() {
            const sunGeometry = new THREE.SphereGeometry(1, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({
                color: 0xffdd00,
                emissive: 0xffdd00,
                emissiveIntensity: 0.5
            });
            const sun = new THREE.Mesh(sunGeometry, sunMaterial);
            sun.position.set(5, 8, -5);

            // Sun rays
            const rayGeometry = new THREE.ConeGeometry(0.1, 5, 4);
            const rayMaterial = new THREE.MeshBasicMaterial({
                color: 0xffdd00,
                transparent: true,
                opacity: 0.3
            });

            const rays = new THREE.Group();
            for (let i = 0; i < 8; i++) {
                const ray = new THREE.Mesh(rayGeometry, rayMaterial);
                ray.position.copy(sun.position);
                ray.rotation.z = (Math.PI * 2 / 8) * i;
                rays.add(ray);
            }

            const sunGroup = new THREE.Group();
            sunGroup.add(sun);
            sunGroup.add(rays);

            return sunGroup;
        }

        camera.position.set(0, 10, 15);
        camera.lookAt(0,0,0);

        // --- UI Interaction & Data Handling (Flask Integration) ---
        const sourceButtons = document.querySelectorAll('.source-button');
        const capXmlInput = document.getElementById('cap-xml-input');
        const submitCapXmlButton = document.getElementById('submit-cap-xml');
        const capInputArea = document.getElementById('cap-input-area');

        const weatherAdvisoryEl = document.getElementById('weather-advisory');
        const agroAdvisoryEl = document.getElementById('agro-advisory');
        const capDisplayEl = document.getElementById('cap-alert-display');
        const weatherIconEl = weatherAdvisoryEl.querySelector('.advisory-icon');

        function showLoading(button, panelId) {
            if(button) button.classList.add('loading');
            const panel = document.getElementById(panelId);
            if (panel) {
                const p = panel.querySelector('p');
                if(p) p.textContent = 'Fetching data...';
                const h3 = panel.querySelector('h3#cap-headline');
                if(h3) h3.textContent = 'Processing CAP...';
            }
        }

        function hideLoading(button) {
            if(button) button.classList.remove('loading');
        }

        function updateUI(sourceKey, data) {
            console.log("Updating UI for:", sourceKey, "with data:", data);

            weatherAdvisoryEl.style.display = 'none';
            agroAdvisoryEl.style.display = 'none';
            capDisplayEl.style.display = 'none';
            capInputArea.style.display = 'none';

            if (data && data.error) {
                console.error("Error from backend:", data.error);
                let errorText = data.advisory_text || data.error || "An unknown error occurred.";
                if (sourceKey === 'meteo') {
                    weatherAdvisoryEl.style.display = 'block';
                    document.getElementById('weather-text').textContent = `Error: ${errorText}`;
                    if(weatherIconEl) weatherIconEl.textContent = '⚠️';
                } else if (sourceKey === 'agro') {
                    agroAdvisoryEl.style.display = 'block';
                    document.getElementById('agro-text').textContent = `Error: ${errorText}`;
                } else if (sourceKey === 'cap') {
                    capDisplayEl.style.display = 'block';
                    document.getElementById('cap-headline').textContent = 'CAP Processing Error';
                    document.getElementById('cap-description').textContent = errorText;
                }
                return;
            }

            // If no error, proceed to update with data
            if (sourceKey === 'meteo' && data && data.advisory_text) {
                weatherAdvisoryEl.style.display = 'block';
                document.getElementById('weather-text').textContent = data.advisory_text;
                if(weatherIconEl && data.icon_emoji) weatherIconEl.textContent = data.icon_emoji;

                // Update weather effect based on weather data
                if (currentWeatherEffect) {
                    scene.remove(currentWeatherEffect);
                    currentWeatherEffect = null;
                }

                if (data.weather_code !== undefined) {
                    if (data.weather_code >= 0 && data.weather_code <= 1) {
                        currentWeatherEffect = createSunEffect(); // Sunny
                    } else if (data.weather_code >= 51 || data.weather_code >= 95) {
                        currentWeatherEffect = createRainEffect(); // Rainy/Stormy
                    }

                    if (currentWeatherEffect) {
                        scene.add(currentWeatherEffect);
                    }
                }
            } else if (sourceKey === 'agro' && data && data.advisory_text) {
                agroAdvisoryEl.style.display = 'block';
                document.getElementById('agro-text').textContent = data.advisory_text;
            } else if (sourceKey === 'cap_input') {
                capInputArea.style.display = 'block';
            } else if (sourceKey === 'cap' && data && data.headline) {
                capDisplayEl.style.display = 'block';
                document.getElementById('cap-headline').textContent = data.headline;
                document.getElementById('cap-description').textContent = data.description;
                document.getElementById('cap-area').textContent = `Area: ${data.area_description || 'N/A'}`;
                document.getElementById('cap-sender').textContent = `Sender: ${data.sender || 'N/A'}`;
                document.getElementById('cap-sent-time').textContent = `Sent: ${data.sent_time_cap ? new Date(data.sent_time_cap).toLocaleString() : 'N/A'}`;
            }

            // Update status bar if data contains such fields
            if (data && data.alerts !== undefined) {
                document.getElementById('active-alerts').textContent = data.alerts || 0;
            }
            if (data && data.coverage !== undefined) {
                document.getElementById('coverage').textContent = data.coverage || 0;
            }
            if (data && data.responseTime !== undefined) {
                document.getElementById('response-time').textContent = data.responseTime || '--';
            }

            gsap.fromTo('.advisory-card:not([style*="display: none"])', {opacity: 0, y:20}, {opacity:1, y:0, duration:0.5, stagger:0.1});
        }

        sourceButtons.forEach(button => {
            if (button.disabled) return; // Skip disabled buttons

            button.addEventListener('click', async () => {
                const source = button.dataset.source;
                sourceButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                weatherAdvisoryEl.style.display = 'none';
                agroAdvisoryEl.style.display = 'none';
                capDisplayEl.style.display = 'none';
                capInputArea.style.display = 'none';

                if (source === 'cap') {
                    updateUI('cap_input', {});
                    return;
                }

                // Skip animation for unsupported sources
                if (!['meteo', 'agro'].includes(source)) {
                    let panelIdToLoad = '';
                    if (source === 'meteo') panelIdToLoad = 'weather-advisory';
                    else if (source === 'agro') panelIdToLoad = 'agro-advisory';
                    showLoading(button, panelIdToLoad);

                    try {
                        const response = await fetch(`/fetch_data?source=${source}`);
                        hideLoading(button);
                        if (!response.ok) {
                            throw new Error(`HTTP error ${response.status}`);
                        }
                        const data = await response.json();
                        updateUI(source, data);
                    } catch (error) {
                        console.error(`Error fetching ${source} data:`, error);
                        hideLoading(button);
                        updateUI(source, { error: error.message, advisory_text: `Failed to load ${source} data.` });
                    }
                    return;
                }

                // Show process animation for supported sources
                const sourceNames = {
                    'meteo': 'Weather',
                    'agro': 'Agricultural'
                };

                processAnimation.show(source, `Fetching ${sourceNames[source]} Data`);

                try {
                    const response = await fetch(`/fetch_data?source=${source}`); // Fetch from Flask backend

                    if (!response.ok) {
                        let errorData;
                        try {
                            errorData = await response.json();
                        } catch (e) {
                            errorData = { error: `HTTP error ${response.status}. Could not parse error response.` };
                        }
                        throw new Error(errorData.advisory_text || errorData.error || `HTTP error ${response.status}`);
                    }
                    const data = await response.json();

                    // Let the animation complete, then update UI
                    setTimeout(() => {
                        updateUI(source, data);
                    }, 500);

                } catch (error) {
                    console.error(`Error fetching ${source} data:`, error);
                    processAnimation.setError(`Failed to load ${sourceNames[source]} data: ${error.message}`);

                    // Still update UI with error after animation
                    setTimeout(() => {
                        updateUI(source, { error: error.message, advisory_text: `Failed to load ${source} data. Server might be down or an error occurred.` });
                    }, 2500);
                }
            });
        });

        if (submitCapXmlButton) {
            submitCapXmlButton.addEventListener('click', async () => {
                const capXml = capXmlInput.value;
                if (!capXml.trim()) {
                    alert("Please paste CAP XML data into the text area.");
                    return;
                }

                capInputArea.style.display = 'none';

                // Show process animation for CAP processing
                processAnimation.show('cap', 'Processing CAP Alert');

                try {
                    const response = await fetch('/submit_cap', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/xml' },
                        body: capXml
                    });
                     if (!response.ok) {
                        let errorData;
                        try {
                            errorData = await response.json();
                        } catch(e){
                             errorData = { error: `HTTP error ${response.status}. Could not parse error response.`};
                        }
                        throw new Error(errorData.advisory_text || errorData.error || `HTTP error ${response.status}`);
                    }
                    const data = await response.json();

                    // Let the animation complete, then update UI
                    setTimeout(() => {
                        updateUI('cap', data);
                    }, 500);

                } catch (error) {
                    console.error('Error submitting CAP XML:', error);
                    processAnimation.setError(`Failed to process CAP alert: ${error.message}`);

                    // Still update UI with error after animation
                    setTimeout(() => {
                        updateUI('cap', { error: error.message, advisory_text: 'Failed to process CAP alert.' });
                    }, 2500);
                }
            });
        }

        function animate() {
            requestAnimationFrame(animate);

            // Map rotation is now stopped
            // if (ghanaMapGroup) {
            //     ghanaMapGroup.rotation.y += 0.001;
            // }

            // Animate rain
            if (currentWeatherEffect && currentWeatherEffect.children) {
                currentWeatherEffect.children.forEach(line => {
                    if (line.geometry && line.geometry.attributes && line.geometry.attributes.position) {
                        const positions = line.geometry.attributes.position.array;
                        positions[1] -= 0.3; // Move top point down
                        positions[4] -= 0.3; // Move bottom point down

                        // Reset rain when it goes below ground
                        if (positions[4] < -5) {
                            positions[1] = Math.random() * 15;
                            positions[4] = positions[1] - 0.5;
                        }

                        line.geometry.attributes.position.needsUpdate = true;
                    }
                });
            }

            // Animate sun rays
            if (currentWeatherEffect && currentWeatherEffect.children && currentWeatherEffect.children[0] && currentWeatherEffect.children[0].type === 'Mesh') {
                currentWeatherEffect.rotation.y += 0.01;
            }

            renderer.render(scene, camera);
        }

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        animate();

        // Initialize visibility
        function initializeVisibility() {
            // Ensure all glass-card elements are visible
            const glassCards = document.querySelectorAll('.glass-card');
            glassCards.forEach(card => {
                card.style.opacity = '1';
                card.style.visibility = 'visible';
            });

            // Show weather advisory by default
            const weatherAdvisory = document.getElementById('weather-advisory');
            weatherAdvisory.style.display = 'block';
            weatherAdvisory.style.opacity = '1';
        }

        // Initialize visibility immediately
        initializeVisibility();

        setTimeout(() => {
            document.getElementById('loading').classList.add('hidden');
            gsap.from('.header h1', { y: -50, opacity: 0, duration: 1, ease: 'power3.out' });
            gsap.from('.glass-card', { scale: 0.8, opacity: 0, duration: 0.8, stagger: 0.1, ease: 'power3.out', delay: 0.3 });

            weatherAdvisoryEl.style.display = 'block';
            document.getElementById('weather-text').textContent = "Select a data source or input a CAP alert.";
            agroAdvisoryEl.style.display = 'none';
            capDisplayEl.style.display = 'none';
            capInputArea.style.display = 'none';

        }, 1000);

    </script>
</body>
</html>
